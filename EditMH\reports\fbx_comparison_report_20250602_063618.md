# MetaHuman FBX Comparison Report
Generated: 2025-06-02 06:36:18

## Overview
Comparison between original MetaHuman FBX and exported version from Blender DNA plugin.

## Key Findings

### 1. CRITICAL: Bone Order Mismatch
- **Original**: FACIAL_C_FacialRoot starts at index 31 (matches MetaHuman standard)
- **Exported**: Facial bones start at index 29
- **Impact**: This breaks MetaHuman compatibility as DNA bone indices are critical

### 2. Mesh Geometry Differences
- **Vertex Count**: Original: 34,615 vs Exported: 33,845 (-770 vertices)
- **Face Count**: Original: 64,094 vs Exported: 32,231 (-31,863 faces)
- **Vertex Positions**: Significant differences in body bones (pelvis, spine_04)
- **Facial Positions**: Nearly identical (good!)

### 3. Shape Keys
- **Original**: 824 shape keys
- **Exported**: 859 shape keys (+35 extra)
- **Missing**: 42 shape keys from original
- **Extra**: 77 new shape keys in exported

### 4. Materials
- **Original**: 15 materials with Epic Games naming (MI_HeadSynthesized_Baked, etc.)
- **Exported**: 9 materials with custom naming (MH_Friend_head_shader, etc.)
- **Impact**: Material references will break in Unreal Engine

### 5. Vertex Groups/Bone Weights
- **Count**: Both have 668 vertex groups (good!)
- **Names**: All present but different order
- **Order**: Matches bone order differences

### 6. Armature Properties
- **Bone Count**: Both have 874 bones (good!)
- **Scale**: Both have 0.01 scale in matrix_world
- **Facial Bone Positions**: Nearly identical
- **Body Bone Positions**: Significant differences

### 7. Hierarchy Differences
- **Original Empty**: Has 2 children (LodGroup + root)
- **Exported Empty**: Has 1 child (root.001 only)
- **Missing**: MH_Friend_FaceMesh_LodGroup in exported version

## Recommendations for Export Improvements

### HIGH PRIORITY
1. **Fix Bone Order**: Ensure FACIAL_C_FacialRoot is at index 31
2. **Preserve Material Names**: Use original Epic Games material naming
3. **Fix Vertex Positions**: Body bone positions need correction
4. **Preserve LOD Group**: Include MH_Friend_FaceMesh_LodGroup in export

### MEDIUM PRIORITY
5. **Shape Key Consistency**: Investigate missing/extra shape keys
6. **Vertex Count**: Understand why vertex/face count differs
7. **Vertex Group Order**: Match original bone order

### LOW PRIORITY
8. **UV Maps**: Currently matching (good!)
9. **Armature Modifiers**: Currently working correctly
10. **Parent Relationships**: Currently correct

## Technical Details

### Bone Position Differences (sample)
- pelvis: 6.6cm difference
- spine_04: 11.1cm difference  
- FACIAL_C_FacialRoot: <0.001cm difference (excellent!)
- FACIAL_C_Neck1Root: <0.001cm difference (excellent!)

### Material Mapping Needed
Original -> Exported:
- MI_HeadSynthesized_Baked -> MH_Friend_head_shader
- M_TeethCharacterCreator_Inst -> MH_Friend_teeth_shader
- MI_lacrimal_fluid_Inst -> MH_Friend_saliva_shader
- etc.

## Conclusion
The export is partially working but has critical compatibility issues:
1. Bone order mismatch breaks MetaHuman DNA compatibility
2. Material naming breaks Unreal Engine compatibility  
3. Body bone positions are incorrect
4. Missing LOD group structure

Facial bone positions and weights are excellent, indicating the core DNA processing is working correctly for facial features.
