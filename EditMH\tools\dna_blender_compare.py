#!/usr/bin/env python3
"""
DNA Comparison using Blender Environment

This script should be run from within Blender to access DNA reading capabilities.
"""

import bpy
import sys
import json
from pathlib import Path
from datetime import datetime

def setup_dna_environment():
    """Setup DNA environment within Blender"""
    # Add plugin path
    plugin_path = Path(bpy.path.abspath("//")) / "plugin" / "blender_dna_plugin"
    if plugin_path.exists() and str(plugin_path) not in sys.path:
        sys.path.insert(0, str(plugin_path))
    
    # Add example path
    example_path = Path(bpy.path.abspath("//")) / "example" / "meta-human-dna-addon-main" / "src" / "addons"
    if example_path.exists() and str(example_path) not in sys.path:
        sys.path.insert(0, str(example_path))

def load_dna_reader(dna_path):
    """Load DNA file using Blender environment"""
    try:
        from utils.dna_utils import check_dna_modules, ensure_dna_modules_path
        
        # Ensure DNA modules are available
        if not check_dna_modules():
            ensure_dna_modules_path()
        
        # Import DNA modules
        import riglogic
        
        # Create file stream
        stream = riglogic.FileStream.create(
            path=str(dna_path),
            accessMode=riglogic.AccessMode.Read,
            openMode=riglogic.OpenMode.Binary,
            memRes=None
        )
        
        # Create reader
        reader = riglogic.BinaryStreamReader.create(
            stream,
            riglogic.DataLayer.All,
            riglogic.UnknownLayerPolicy.Preserve,
            0,  # maxLOD
            None  # memRes
        )
        
        # Read the DNA
        reader.read()
        
        if not riglogic.Status.isOk():
            status = riglogic.Status.get()
            raise RuntimeError(f"Error loading DNA: {status.message}")
        
        return reader
        
    except Exception as e:
        print(f"Error loading DNA file {dna_path}: {e}")
        return None

def analyze_dna_detailed(reader, name):
    """Detailed analysis of DNA file"""
    if not reader:
        return None
    
    print(f"\nAnalyzing {name}...")
    
    try:
        analysis = {
            'name': name,
            'joint_count': reader.getJointCount(),
            'mesh_count': reader.getMeshCount(),
            'blend_shape_count': reader.getBlendShapeChannelCount(),
            'animated_map_count': reader.getAnimatedMapCount(),
            'lod_count': reader.getLODCount(),
        }
        
        # Get joint names
        analysis['joint_names'] = [reader.getJointName(i) for i in range(reader.getJointCount())]
        
        # Get mesh names  
        analysis['mesh_names'] = [reader.getMeshName(i) for i in range(reader.getMeshCount())]
        
        # Get blend shape names
        analysis['blend_shape_names'] = [reader.getBlendShapeChannelName(i) for i in range(reader.getBlendShapeChannelCount())]
        
        # Get animated map names
        analysis['animated_map_names'] = [reader.getAnimatedMapName(i) for i in range(reader.getAnimatedMapCount())]
        
        # Get joint hierarchy
        analysis['joint_parents'] = [reader.getJointParentIndex(i) for i in range(reader.getJointCount())]
        
        # Get neutral joint transforms
        analysis['joint_translations'] = {
            'x': list(reader.getNeutralJointTranslationXs()),
            'y': list(reader.getNeutralJointTranslationYs()),
            'z': list(reader.getNeutralJointTranslationZs())
        }
        
        analysis['joint_rotations'] = {
            'x': list(reader.getNeutralJointRotationXs()),
            'y': list(reader.getNeutralJointRotationYs()),
            'z': list(reader.getNeutralJointRotationZs())
        }
        
        print(f"  Joints: {analysis['joint_count']}")
        print(f"  Meshes: {analysis['mesh_count']}")
        print(f"  Blend Shapes: {analysis['blend_shape_count']}")
        print(f"  Animated Maps: {analysis['animated_map_count']}")
        print(f"  LODs: {analysis['lod_count']}")
        
        return analysis
        
    except Exception as e:
        print(f"Error analyzing DNA: {e}")
        return None

def compare_dna_data(original, exported):
    """Compare DNA data and report differences"""
    print(f"\n{'='*80}")
    print("DETAILED DNA COMPARISON")
    print(f"{'='*80}")
    
    # Basic counts
    print(f"\nBasic Counts:")
    print(f"  Joints: {original['joint_count']} → {exported['joint_count']}")
    print(f"  Meshes: {original['mesh_count']} → {exported['mesh_count']}")
    print(f"  Blend Shapes: {original['blend_shape_count']} → {exported['blend_shape_count']}")
    print(f"  Animated Maps: {original['animated_map_count']} → {exported['animated_map_count']}")
    print(f"  LODs: {original['lod_count']} → {exported['lod_count']}")
    
    # Joint comparison
    original_joints = set(original['joint_names'])
    exported_joints = set(exported['joint_names'])
    
    missing_joints = original_joints - exported_joints
    extra_joints = exported_joints - original_joints
    common_joints = original_joints & exported_joints
    
    print(f"\nJoint Comparison:")
    print(f"  Common joints: {len(common_joints)}")
    print(f"  Missing in exported: {len(missing_joints)}")
    print(f"  Extra in exported: {len(extra_joints)}")
    
    if missing_joints:
        print(f"  Missing joints: {list(missing_joints)[:10]}{'...' if len(missing_joints) > 10 else ''}")
    
    if extra_joints:
        print(f"  Extra joints: {list(extra_joints)[:10]}{'...' if len(extra_joints) > 10 else ''}")
    
    # Mesh comparison
    original_meshes = set(original['mesh_names'])
    exported_meshes = set(exported['mesh_names'])
    
    missing_meshes = original_meshes - exported_meshes
    extra_meshes = exported_meshes - original_meshes
    common_meshes = original_meshes & exported_meshes
    
    print(f"\nMesh Comparison:")
    print(f"  Common meshes: {len(common_meshes)}")
    print(f"  Missing in exported: {len(missing_meshes)}")
    print(f"  Extra in exported: {len(extra_meshes)}")
    
    if missing_meshes:
        print(f"  Missing meshes: {list(missing_meshes)}")
    
    if extra_meshes:
        print(f"  Extra meshes: {list(extra_meshes)}")
    
    # Blend shape comparison
    original_shapes = set(original['blend_shape_names'])
    exported_shapes = set(exported['blend_shape_names'])
    
    missing_shapes = original_shapes - exported_shapes
    extra_shapes = exported_shapes - original_shapes
    common_shapes = original_shapes & exported_shapes
    
    print(f"\nBlend Shape Comparison:")
    print(f"  Common shapes: {len(common_shapes)}")
    print(f"  Missing in exported: {len(missing_shapes)}")
    print(f"  Extra in exported: {len(extra_shapes)}")
    
    if missing_shapes:
        print(f"  Missing shapes: {list(missing_shapes)[:10]}{'...' if len(missing_shapes) > 10 else ''}")
    
    if extra_shapes:
        print(f"  Extra shapes: {list(extra_shapes)[:10]}{'...' if len(extra_shapes) > 10 else ''}")
    
    # Joint transform comparison
    if original['joint_count'] == exported['joint_count']:
        print(f"\nJoint Transform Comparison:")
        translation_diffs = 0
        rotation_diffs = 0
        
        for i in range(original['joint_count']):
            # Translation differences
            tx_diff = abs(original['joint_translations']['x'][i] - exported['joint_translations']['x'][i])
            ty_diff = abs(original['joint_translations']['y'][i] - exported['joint_translations']['y'][i])
            tz_diff = abs(original['joint_translations']['z'][i] - exported['joint_translations']['z'][i])
            
            if tx_diff > 0.001 or ty_diff > 0.001 or tz_diff > 0.001:
                translation_diffs += 1
                if translation_diffs <= 5:
                    joint_name = original['joint_names'][i]
                    print(f"    Translation diff in {joint_name}: ({tx_diff:.6f}, {ty_diff:.6f}, {tz_diff:.6f})")
            
            # Rotation differences
            rx_diff = abs(original['joint_rotations']['x'][i] - exported['joint_rotations']['x'][i])
            ry_diff = abs(original['joint_rotations']['y'][i] - exported['joint_rotations']['y'][i])
            rz_diff = abs(original['joint_rotations']['z'][i] - exported['joint_rotations']['z'][i])
            
            if rx_diff > 0.001 or ry_diff > 0.001 or rz_diff > 0.001:
                rotation_diffs += 1
                if rotation_diffs <= 5:
                    joint_name = original['joint_names'][i]
                    print(f"    Rotation diff in {joint_name}: ({rx_diff:.6f}, {ry_diff:.6f}, {rz_diff:.6f})")
        
        print(f"  Total translation differences: {translation_diffs}")
        print(f"  Total rotation differences: {rotation_diffs}")
    
    return {
        'joints': {'missing': missing_joints, 'extra': extra_joints, 'common': common_joints},
        'meshes': {'missing': missing_meshes, 'extra': extra_meshes, 'common': common_meshes},
        'blend_shapes': {'missing': missing_shapes, 'extra': extra_shapes, 'common': common_shapes}
    }

def main():
    """Main function to run in Blender"""
    print("DNA Detailed Comparison (Blender Environment)")
    print("=" * 60)
    
    setup_dna_environment()
    
    # DNA file paths (relative to blend file)
    base_path = Path(bpy.path.abspath("//"))
    dna_folder = base_path / "builds" / "DNAs"
    original_dna = dna_folder / "MH_Friend.dna"
    exported_dna = dna_folder / "edited_dna.dna"
    
    print(f"Base path: {base_path}")
    print(f"DNA folder: {dna_folder}")
    
    if not original_dna.exists():
        print(f"❌ Original DNA not found: {original_dna}")
        return
    
    if not exported_dna.exists():
        print(f"❌ Exported DNA not found: {exported_dna}")
        return
    
    print("✅ Both DNA files found")
    
    # Load and analyze both DNA files
    original_reader = load_dna_reader(original_dna)
    exported_reader = load_dna_reader(exported_dna)
    
    if not original_reader:
        print("❌ Failed to load original DNA")
        return
    
    if not exported_reader:
        print("❌ Failed to load exported DNA")
        return
    
    # Analyze both files
    original_analysis = analyze_dna_detailed(original_reader, "Original DNA")
    exported_analysis = analyze_dna_detailed(exported_reader, "Exported DNA")
    
    if not original_analysis or not exported_analysis:
        print("❌ Failed to analyze DNA files")
        return
    
    # Compare the data
    differences = compare_dna_data(original_analysis, exported_analysis)
    
    # Save detailed report
    save_detailed_report(original_analysis, exported_analysis, differences)

def save_detailed_report(original, exported, differences):
    """Save detailed comparison report"""
    base_path = Path(bpy.path.abspath("//"))
    report_folder = base_path / "reports"
    report_folder.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = report_folder / f"dna_detailed_comparison_{timestamp}.json"
    
    report_data = {
        'timestamp': timestamp,
        'original_analysis': original,
        'exported_analysis': exported,
        'differences': {
            'joints': {
                'missing': list(differences['joints']['missing']),
                'extra': list(differences['joints']['extra']),
                'common': list(differences['joints']['common'])
            },
            'meshes': {
                'missing': list(differences['meshes']['missing']),
                'extra': list(differences['meshes']['extra']),
                'common': list(differences['meshes']['common'])
            },
            'blend_shapes': {
                'missing': list(differences['blend_shapes']['missing']),
                'extra': list(differences['blend_shapes']['extra']),
                'common': list(differences['blend_shapes']['common'])
            }
        }
    }
    
    with open(report_path, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\nDetailed report saved to: {report_path}")

if __name__ == "__main__":
    main()
