#!/usr/bin/env python3
"""
DNA File Comparison Tool

Compares two DNA files and reports differences in:
- Joint data (bones)
- Mesh data
- Blend shapes
- Skinning data
- Other DNA components
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

def setup_dna_environment():
    """Setup the DNA reading environment"""
    # Add the plugin path for DNA utilities
    plugin_path = Path(__file__).parent.parent / "plugin" / "blender_dna_plugin"
    if plugin_path.exists():
        sys.path.insert(0, str(plugin_path))

    # Add the example path for DNA utilities
    example_path = Path(__file__).parent.parent / "example" / "meta-human-dna-addon-main" / "src" / "addons"
    if example_path.exists():
        sys.path.insert(0, str(example_path))

def load_dna_reader(dna_path):
    """Load a DNA file and return a reader using plugin utilities"""
    try:
        # Try using the plugin's DNA utilities
        from meta_human_dna.dna_io.misc import get_dna_reader

        print(f"Loading DNA file: {dna_path}")
        reader = get_dna_reader(dna_path, 'binary', 'All')
        return reader
    except ImportError as e:
        print(f"Error importing DNA modules from plugin: {e}")
        try:
            # Fallback to direct DNA import
            from dna import DataLayer_All, FileStream, Status, BinaryStreamReader

            print(f"Loading DNA file with direct import: {dna_path}")
            stream = FileStream(str(dna_path), FileStream.AccessMode_Read, FileStream.OpenMode_Binary)
            reader = BinaryStreamReader(stream, DataLayer_All)
            reader.read()

            if not Status.isOk():
                status = Status.get()
                raise RuntimeError(f"Error loading DNA: {status.message}")

            return reader
        except ImportError as e2:
            print(f"Error importing DNA modules: {e2}")
            print("DNA modules not available in this environment")
            return None
    except Exception as e:
        print(f"Error loading DNA file {dna_path}: {e}")
        return None

def analyze_dna_file(reader, name):
    """Analyze a DNA file and extract key information"""
    if not reader:
        return None
    
    print(f"\nAnalyzing {name}...")
    
    analysis = {
        'name': name,
        'joint_count': reader.getJointCount(),
        'mesh_count': reader.getMeshCount(),
        'blend_shape_count': reader.getBlendShapeChannelCount(),
        'animated_map_count': reader.getAnimatedMapCount(),
        'lod_count': reader.getLODCount(),
    }
    
    # Get joint names
    analysis['joint_names'] = [reader.getJointName(i) for i in range(reader.getJointCount())]
    
    # Get mesh names
    analysis['mesh_names'] = [reader.getMeshName(i) for i in range(reader.getMeshCount())]
    
    # Get blend shape names
    analysis['blend_shape_names'] = [reader.getBlendShapeChannelName(i) for i in range(reader.getBlendShapeChannelCount())]
    
    # Get animated map names
    analysis['animated_map_names'] = [reader.getAnimatedMapName(i) for i in range(reader.getAnimatedMapCount())]
    
    # Get joint hierarchy (parent indices)
    analysis['joint_parents'] = [reader.getJointParentIndex(i) for i in range(reader.getJointCount())]
    
    # Get neutral joint positions
    analysis['joint_translations_x'] = reader.getNeutralJointTranslationXs()
    analysis['joint_translations_y'] = reader.getNeutralJointTranslationYs()
    analysis['joint_translations_z'] = reader.getNeutralJointTranslationZs()
    
    # Get neutral joint rotations
    analysis['joint_rotations_x'] = reader.getNeutralJointRotationXs()
    analysis['joint_rotations_y'] = reader.getNeutralJointRotationYs()
    analysis['joint_rotations_z'] = reader.getNeutralJointRotationZs()
    
    print(f"  Joints: {analysis['joint_count']}")
    print(f"  Meshes: {analysis['mesh_count']}")
    print(f"  Blend Shapes: {analysis['blend_shape_count']}")
    print(f"  Animated Maps: {analysis['animated_map_count']}")
    print(f"  LODs: {analysis['lod_count']}")
    
    return analysis

def compare_lists(list1, list2, name, analysis1_name, analysis2_name):
    """Compare two lists and return differences"""
    set1 = set(list1)
    set2 = set(list2)
    
    only_in_1 = set1 - set2
    only_in_2 = set2 - set1
    common = set1 & set2
    
    print(f"\n{name} Comparison:")
    print(f"  {analysis1_name}: {len(list1)} items")
    print(f"  {analysis2_name}: {len(list2)} items")
    print(f"  Common: {len(common)} items")
    
    if only_in_1:
        print(f"  Only in {analysis1_name}: {len(only_in_1)} items")
        if len(only_in_1) <= 10:
            for item in sorted(only_in_1):
                print(f"    - {item}")
        else:
            for item in sorted(list(only_in_1)[:10]):
                print(f"    - {item}")
            print(f"    ... and {len(only_in_1) - 10} more")
    
    if only_in_2:
        print(f"  Only in {analysis2_name}: {len(only_in_2)} items")
        if len(only_in_2) <= 10:
            for item in sorted(only_in_2):
                print(f"    - {item}")
        else:
            for item in sorted(list(only_in_2)[:10]):
                print(f"    - {item}")
            print(f"    ... and {len(only_in_2) - 10} more")
    
    return {
        'only_in_1': only_in_1,
        'only_in_2': only_in_2,
        'common': common
    }

def compare_joint_transforms(analysis1, analysis2):
    """Compare joint transform data between two DNA files"""
    print(f"\nJoint Transform Comparison:")
    
    if analysis1['joint_count'] != analysis2['joint_count']:
        print(f"  ⚠️  Joint count mismatch: {analysis1['joint_count']} vs {analysis2['joint_count']}")
        return
    
    # Compare translations
    translation_diffs = 0
    rotation_diffs = 0
    
    for i in range(analysis1['joint_count']):
        # Check translations
        tx_diff = abs(analysis1['joint_translations_x'][i] - analysis2['joint_translations_x'][i])
        ty_diff = abs(analysis1['joint_translations_y'][i] - analysis2['joint_translations_y'][i])
        tz_diff = abs(analysis1['joint_translations_z'][i] - analysis2['joint_translations_z'][i])
        
        if tx_diff > 0.001 or ty_diff > 0.001 or tz_diff > 0.001:
            translation_diffs += 1
            if translation_diffs <= 10:  # Show first 10 differences
                joint_name = analysis1['joint_names'][i]
                print(f"    Translation diff in {joint_name}: "
                      f"({tx_diff:.6f}, {ty_diff:.6f}, {tz_diff:.6f})")
        
        # Check rotations
        rx_diff = abs(analysis1['joint_rotations_x'][i] - analysis2['joint_rotations_x'][i])
        ry_diff = abs(analysis1['joint_rotations_y'][i] - analysis2['joint_rotations_y'][i])
        rz_diff = abs(analysis1['joint_rotations_z'][i] - analysis2['joint_rotations_z'][i])
        
        if rx_diff > 0.001 or ry_diff > 0.001 or rz_diff > 0.001:
            rotation_diffs += 1
            if rotation_diffs <= 10:  # Show first 10 differences
                joint_name = analysis1['joint_names'][i]
                print(f"    Rotation diff in {joint_name}: "
                      f"({rx_diff:.6f}, {ry_diff:.6f}, {rz_diff:.6f})")
    
    print(f"  Translation differences: {translation_diffs} joints")
    print(f"  Rotation differences: {rotation_diffs} joints")

def main():
    """Main comparison function"""
    setup_dna_environment()
    
    # DNA file paths
    dna_folder = Path(__file__).parent.parent / "builds" / "DNAs"
    original_dna = dna_folder / "MH_Friend.dna"
    exported_dna = dna_folder / "edited_dna.dna"
    
    if not original_dna.exists():
        print(f"Original DNA file not found: {original_dna}")
        return
    
    if not exported_dna.exists():
        print(f"Exported DNA file not found: {exported_dna}")
        return
    
    print("DNA File Comparison Tool")
    print("=" * 50)
    
    # Load both DNA files
    original_reader = load_dna_reader(original_dna)
    exported_reader = load_dna_reader(exported_dna)
    
    if not original_reader or not exported_reader:
        print("Failed to load one or both DNA files")
        return
    
    # Analyze both files
    original_analysis = analyze_dna_file(original_reader, "Original DNA")
    exported_analysis = analyze_dna_file(exported_reader, "Exported DNA")
    
    if not original_analysis or not exported_analysis:
        print("Failed to analyze one or both DNA files")
        return
    
    print(f"\n{'='*80}")
    print("COMPARISON RESULTS")
    print(f"{'='*80}")
    
    # Compare basic counts
    print(f"\nBasic Counts:")
    print(f"  Joints: {original_analysis['joint_count']} → {exported_analysis['joint_count']}")
    print(f"  Meshes: {original_analysis['mesh_count']} → {exported_analysis['mesh_count']}")
    print(f"  Blend Shapes: {original_analysis['blend_shape_count']} → {exported_analysis['blend_shape_count']}")
    print(f"  Animated Maps: {original_analysis['animated_map_count']} → {exported_analysis['animated_map_count']}")
    print(f"  LODs: {original_analysis['lod_count']} → {exported_analysis['lod_count']}")
    
    # Compare lists
    joint_diff = compare_lists(original_analysis['joint_names'], exported_analysis['joint_names'], 
                              "Joints", "Original", "Exported")
    
    mesh_diff = compare_lists(original_analysis['mesh_names'], exported_analysis['mesh_names'], 
                             "Meshes", "Original", "Exported")
    
    blend_shape_diff = compare_lists(original_analysis['blend_shape_names'], exported_analysis['blend_shape_names'], 
                                    "Blend Shapes", "Original", "Exported")
    
    animated_map_diff = compare_lists(original_analysis['animated_map_names'], exported_analysis['animated_map_names'], 
                                     "Animated Maps", "Original", "Exported")
    
    # Compare joint transforms
    compare_joint_transforms(original_analysis, exported_analysis)
    
    # Save detailed report
    save_detailed_report(original_analysis, exported_analysis, joint_diff, mesh_diff, blend_shape_diff, animated_map_diff)

def save_detailed_report(original_analysis, exported_analysis, joint_diff, mesh_diff, blend_shape_diff, animated_map_diff):
    """Save a detailed comparison report"""
    report_folder = Path(__file__).parent.parent / "reports"
    report_folder.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = report_folder / f"dna_comparison_report_{timestamp}.json"
    
    report_data = {
        'timestamp': timestamp,
        'original_analysis': original_analysis,
        'exported_analysis': exported_analysis,
        'differences': {
            'joints': {
                'only_in_original': list(joint_diff['only_in_1']),
                'only_in_exported': list(joint_diff['only_in_2']),
                'common': list(joint_diff['common'])
            },
            'meshes': {
                'only_in_original': list(mesh_diff['only_in_1']),
                'only_in_exported': list(mesh_diff['only_in_2']),
                'common': list(mesh_diff['common'])
            },
            'blend_shapes': {
                'only_in_original': list(blend_shape_diff['only_in_1']),
                'only_in_exported': list(blend_shape_diff['only_in_2']),
                'common': list(blend_shape_diff['common'])
            },
            'animated_maps': {
                'only_in_original': list(animated_map_diff['only_in_1']),
                'only_in_exported': list(animated_map_diff['only_in_2']),
                'common': list(animated_map_diff['common'])
            }
        }
    }
    
    with open(report_path, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\nDetailed report saved to: {report_path}")

if __name__ == "__main__":
    main()
