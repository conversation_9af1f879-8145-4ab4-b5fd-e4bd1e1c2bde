"""
Constants for the Blender MetaHuman DNA Plugin.
"""

import os
import math
from pathlib import Path
from mathutils import Vector, Euler

# Custom bone shape scale
CUSTOM_BONE_SHAPE_NAME = "MetaHuman_BoneShape"
CUSTOM_BONE_SHAPE_SCALE = Vector((1.25, 1.25, 1.25))

# Extra bones to add to the armature
# Updated with correct MetaHuman transforms (from original FBX analysis)
# Removed 'head', 'neck_01', 'spine_04', and 'face' to avoid conflicts with DNA bones
EXTRA_BONES = [
    ('pelvis', {
        'parent': None,  # Pelvis is the root bone
        'location': Vector((0.0000, -2.0948, 87.0708)),
        'rotation': E<PERSON>r((math.radians(-90.0), math.radians(-2.053), math.radians(90.0)), 'XYZ')
    }),
    ('spine_01', {
        'parent': 'pelvis',
        'location': Vector((2.0312, -1.9294, -0.0000)),
        'rotation': Euler((math.radians(-90.0), math.radians(-13.003), math.radians(90.0)), 'XYZ')
    }),
    ('spine_02', {
        'parent': 'spine_01',
        'location': Vector((4.2676, -4.2676, 0.0000)),
        'rotation': Euler((math.radians(-90.0), math.radians(-5.68216), math.radians(90.0)), 'XYZ')
    }),
    ('spine_03', {
        'parent': 'spine_02',
        'location': Vector((6.7545, -6.7545, -0.0000)),
        'rotation': Euler((math.radians(-90.0), math.radians(3.82404), math.radians(90.0)), 'XYZ')
    }),
]

# Bone collections
class BoneCollection:
    # Further simplified bone collections as requested - only two collections
    FACE_BONES = "Face Bones"    # All facial bones
    BODY_BONES = "Body Bones"    # All non-face bones

# Faceboard constants
FACE_BOARD_NAME = "face_gui"
RESOURCES_FOLDER = Path(os.path.dirname(__file__), "resources")
BLENDS_FOLDER = RESOURCES_FOLDER / "blends"
POSES_FOLDER = RESOURCES_FOLDER / "poses"
FACE_BOARD_FILE_PATH = BLENDS_FOLDER / "face_board.blend"
FACE_GUI_EMPTIES = [
    "GRP_C_eyesAim",
    "GRP_faceGUI",
    "LOC_C_eyeDriver",
    "head_grp",
    "headRig_grp",
    "headGui_grp",
    "headRigging_grp",
    "eyesSetup_grp"
]
