"""
Constants for the Blender MetaHuman DNA Plugin.
"""

import os
import math
from pathlib import Path
from mathutils import Vector, Euler

# Custom bone shape scale
CUSTOM_BONE_SHAPE_NAME = "MetaHuman_BoneShape"
CUSTOM_BONE_SHAPE_SCALE = Vector((1.25, 1.25, 1.25))

# Set to <PERSON>'s height, but locations will be scaled proportionally to match spine_04 location from DNA file
# Also in Y-up coordinate system like the metahuman creator DNA files
FIRST_BONE_Y_LOCATION = 107.86403

# Extra bones to add to the armature
# Values from @EditMH\example\ reference implementation
# Set to <PERSON>'s height, but locations will be scaled proportionally to match spine_04 location from DNA file
# Also in Y-up coordinate system like the metahuman creator DNA files
# Removed 'root' bone since we don't need it and it causes issues during export
EXTRA_BONES = [
    ('pelvis', {
        'parent': None,  # Pelvis becomes the root bone (was 'root' in example)
        'location': Vector((0.0, 0.8707, 0.0209)),
        'rotation': <PERSON><PERSON><PERSON>((math.radians(-90.0), math.radians(-2.053), math.radians(90.0)), 'XYZ')
    }),
    ('spine_01', {
        'parent': 'pelvis',
        'location': Vector((0.0, 0.8910, 0.0206)),
        'rotation': Euler((math.radians(-90.0), math.radians(-13.003), math.radians(90.0)), 'XYZ')
    }),
    ('spine_02', {
        'parent': 'spine_01',
        'location': Vector((0.0, 0.9326, 0.0302)),
        'rotation': Euler((math.radians(-90.0), math.radians(-5.68216), math.radians(90.0)), 'XYZ')
    }),
    ('spine_03', {
        'parent': 'spine_02',
        'location': Vector((0.0, 0.9998, 0.0369)),
        'rotation': Euler((math.radians(-90.0), math.radians(3.82404), math.radians(90.0)), 'XYZ')
    }),
]

# Bone collections
class BoneCollection:
    # Further simplified bone collections as requested - only two collections
    FACE_BONES = "Face Bones"    # All facial bones
    BODY_BONES = "Body Bones"    # All non-face bones

# Faceboard constants
FACE_BOARD_NAME = "face_gui"
RESOURCES_FOLDER = Path(os.path.dirname(__file__), "resources")
BLENDS_FOLDER = RESOURCES_FOLDER / "blends"
POSES_FOLDER = RESOURCES_FOLDER / "poses"
FACE_BOARD_FILE_PATH = BLENDS_FOLDER / "face_board.blend"
FACE_GUI_EMPTIES = [
    "GRP_C_eyesAim",
    "GRP_faceGUI",
    "LOC_C_eyeDriver",
    "head_grp",
    "headRig_grp",
    "headGui_grp",
    "headRigging_grp",
    "eyesSetup_grp"
]
